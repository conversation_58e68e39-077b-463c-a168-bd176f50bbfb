rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read and write their own user document
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Roadmap access rules
    match /roadmaps/{roadmapId} {
      // Users can read their own roadmaps (private or public)
      allow read: if request.auth != null && 
        request.auth.uid == resource.data.userId;
      
      // Users can read public roadmaps from any user
      allow read: if resource.data.isPublic == true;
      
      // Users can write (create, update, delete) only their own roadmaps
      allow write: if request.auth != null && 
        request.auth.uid == resource.data.userId;
      
      // Allow users to create new roadmaps with their own userId
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.userId;
    }
    
    // Roadmap metadata collection for efficient querying
    match /roadmapMetadata/{roadmapId} {
      // Users can read their own roadmap metadata
      allow read: if request.auth != null && 
        request.auth.uid == resource.data.userId;
      
      // Users can read public roadmap metadata
      allow read: if resource.data.isPublic == true;
      
      // Users can write only their own roadmap metadata
      allow write: if request.auth != null && 
        request.auth.uid == resource.data.userId;
      
      // Allow users to create new roadmap metadata with their own userId
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.userId;
    }
    
    // Task completion tracking
    match /taskCompletions/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      
      // Allow reading completion data for public roadmaps (for statistics)
      match /roadmaps/{roadmapId} {
        allow read: if exists(/databases/$(database)/documents/roadmaps/$(roadmapId)) &&
          get(/databases/$(database)/documents/roadmaps/$(roadmapId)).data.isPublic == true;
      }
    }
    
    // User preferences and settings
    match /userPreferences/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Public roadmap statistics (read-only for all authenticated users)
    match /publicStats/{document=**} {
      allow read: if request.auth != null;
      allow write: if false; // Only server-side functions can write stats
    }
  }
}
