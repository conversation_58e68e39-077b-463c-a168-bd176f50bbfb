/**
 * Firestore Persistence Utility
 * Manages user-scoped roadmap data storage with Firebase Firestore
 */

import {
  collection,
  doc,
  getDoc,
  getDocs,
  setDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  serverTimestamp,
  writeBatch,
  onSnapshot,
  increment,
} from "firebase/firestore";
import { db } from "../config/firebase";

// Compression utilities
const compressData = async (data) => {
  try {
    const jsonString = JSON.stringify(data);
    const encoder = new TextEncoder();
    const uint8Array = encoder.encode(jsonString);

    // Use CompressionStream if available (modern browsers)
    if ("CompressionStream" in window) {
      const compressionStream = new CompressionStream("gzip");
      const writer = compressionStream.writable.getWriter();
      const reader = compressionStream.readable.getReader();

      writer.write(uint8Array);
      writer.close();

      const chunks = [];
      let done = false;

      while (!done) {
        const { value, done: readerDone } = await reader.read();
        done = readerDone;
        if (value) {
          chunks.push(value);
        }
      }

      // Convert to base64 for storage
      const compressed = new Uint8Array(
        chunks.reduce((acc, chunk) => acc + chunk.length, 0)
      );
      let offset = 0;
      for (const chunk of chunks) {
        compressed.set(chunk, offset);
        offset += chunk.length;
      }

      return {
        compressed: btoa(String.fromCharCode(...compressed)),
        isCompressed: true,
        originalSize: jsonString.length,
        compressedSize: compressed.length,
      };
    } else {
      // Fallback: Use simple string compression for older browsers
      return {
        compressed: jsonString,
        isCompressed: false,
        originalSize: jsonString.length,
        compressedSize: jsonString.length,
      };
    }
  } catch (error) {
    console.warn("Compression failed, storing uncompressed:", error);
    const jsonString = JSON.stringify(data);
    return {
      compressed: jsonString,
      isCompressed: false,
      originalSize: jsonString.length,
      compressedSize: jsonString.length,
    };
  }
};

const decompressData = async (compressedData) => {
  try {
    if (!compressedData.isCompressed) {
      return JSON.parse(compressedData.compressed);
    }

    // Decompress using DecompressionStream
    if ("DecompressionStream" in window) {
      const binaryString = atob(compressedData.compressed);
      const uint8Array = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        uint8Array[i] = binaryString.charCodeAt(i);
      }

      const decompressionStream = new DecompressionStream("gzip");
      const writer = decompressionStream.writable.getWriter();
      const reader = decompressionStream.readable.getReader();

      writer.write(uint8Array);
      writer.close();

      const chunks = [];
      let done = false;

      while (!done) {
        const { value, done: readerDone } = await reader.read();
        done = readerDone;
        if (value) {
          chunks.push(value);
        }
      }

      const decompressed = new Uint8Array(
        chunks.reduce((acc, chunk) => acc + chunk.length, 0)
      );
      let offset = 0;
      for (const chunk of chunks) {
        decompressed.set(chunk, offset);
        offset += chunk.length;
      }

      const decoder = new TextDecoder();
      const jsonString = decoder.decode(decompressed);
      return JSON.parse(jsonString);
    } else {
      // Fallback for older browsers
      return JSON.parse(compressedData.compressed);
    }
  } catch (error) {
    console.error("Decompression failed:", error);
    throw new Error("Failed to decompress roadmap data");
  }
};

class FirestorePersistence {
  /**
   * Generate a unique ID for a roadmap
   */
  static generateRoadmapId(title, userId) {
    const timestamp = Date.now();
    const cleanTitle = title.replace(/[^a-zA-Z0-9]/g, "-").toLowerCase();
    const userPrefix = userId.substring(0, 8);
    return `${userPrefix}-${cleanTitle}-${timestamp}`;
  }

  /**
   * Save a roadmap with user ownership
   */
  static async saveRoadmap(roadmapData, userId, originalData = null) {
    if (!userId) {
      throw new Error("User must be authenticated to save roadmaps");
    }

    try {
      const roadmapId = this.generateRoadmapId(roadmapData.title, userId);
      const timestamp = serverTimestamp();

      // Compress the roadmap data to handle large files
      console.log("🗜️ Compressing roadmap data...");
      const compressedData = await compressData(roadmapData);
      const compressedOriginalData = originalData
        ? await compressData(originalData)
        : compressedData;

      console.log(`📊 Compression stats:
        - Original size: ${compressedData.originalSize} bytes
        - Compressed size: ${compressedData.compressedSize} bytes
        - Compression ratio: ${(
          (1 - compressedData.compressedSize / compressedData.originalSize) *
          100
        ).toFixed(1)}%
        - Method: ${compressedData.isCompressed ? "gzip" : "uncompressed"}`);

      // Prepare roadmap document with compressed data
      const roadmapDoc = {
        id: roadmapId,
        userId: userId,
        data: compressedData,
        originalData: compressedOriginalData,
        isPublic: false, // Default to private
        createdAt: timestamp,
        updatedAt: timestamp,
        lastAccessed: timestamp,
        version: 1,
        tags: roadmapData.tags || [],
        projectLevel: roadmapData.project_level || "beginner",
        // Add compression metadata
        compressionInfo: {
          isCompressed: compressedData.isCompressed,
          originalSize: compressedData.originalSize,
          compressedSize: compressedData.compressedSize,
          method: compressedData.isCompressed ? "gzip" : "none",
        },
      };

      // Prepare metadata document for efficient querying
      const metadataDoc = {
        id: roadmapId,
        userId: userId,
        title: roadmapData.title,
        description: roadmapData.description || "",
        projectLevel: roadmapData.project_level || "beginner",
        tags: roadmapData.tags || [],
        isPublic: false,
        createdAt: timestamp,
        updatedAt: timestamp,
        lastAccessed: timestamp,
        totalPhases: this.calculateTotalPhases(roadmapData),
        totalTasks: this.calculateTotalTasks(roadmapData),
        progressPercentage: 0,
        viewCount: 0,
        likeCount: 0,
      };

      // Use batch write for consistency
      const batch = writeBatch(db);

      // Save roadmap document
      const roadmapRef = doc(db, "roadmaps", roadmapId);
      batch.set(roadmapRef, roadmapDoc);

      // Save metadata document
      const metadataRef = doc(db, "roadmapMetadata", roadmapId);
      batch.set(metadataRef, metadataDoc);

      await batch.commit();

      console.log("✅ Roadmap saved to Firestore:", roadmapId);
      return roadmapId;
    } catch (error) {
      console.error("❌ Error saving roadmap to Firestore:", error);
      throw new Error("Failed to save roadmap: " + error.message);
    }
  }

  /**
   * Load a specific roadmap
   */
  static async loadRoadmap(roadmapId, userId = null) {
    try {
      const roadmapRef = doc(db, "roadmaps", roadmapId);
      const roadmapSnap = await getDoc(roadmapRef);

      if (!roadmapSnap.exists()) {
        return null;
      }

      const roadmapData = roadmapSnap.data();

      // Check access permissions
      if (!roadmapData.isPublic && (!userId || roadmapData.userId !== userId)) {
        throw new Error("Access denied: This roadmap is private");
      }

      // Decompress the roadmap data if it's compressed
      let decompressedData = roadmapData.data;
      let decompressedOriginalData = roadmapData.originalData;

      if (
        roadmapData.data &&
        typeof roadmapData.data === "object" &&
        roadmapData.data.compressed
      ) {
        console.log("🗜️ Decompressing roadmap data...");
        try {
          decompressedData = await decompressData(roadmapData.data);
          if (roadmapData.originalData && roadmapData.originalData.compressed) {
            decompressedOriginalData = await decompressData(
              roadmapData.originalData
            );
          }
          console.log("✅ Roadmap data decompressed successfully");
        } catch (decompressError) {
          console.error(
            "❌ Failed to decompress roadmap data:",
            decompressError
          );
          throw new Error("Failed to decompress roadmap data");
        }
      }

      // Update last accessed time if user owns the roadmap
      if (userId && roadmapData.userId === userId) {
        await this.updateLastAccessed(roadmapId);
      }

      return {
        id: roadmapData.id,
        data: decompressedData,
        originalData: decompressedOriginalData,
        isPublic: roadmapData.isPublic,
        userId: roadmapData.userId,
        createdAt: roadmapData.createdAt,
        updatedAt: roadmapData.updatedAt,
        lastAccessed: roadmapData.lastAccessed,
        compressionInfo: roadmapData.compressionInfo,
      };
    } catch (error) {
      console.error("❌ Error loading roadmap from Firestore:", error);
      throw error;
    }
  }

  /**
   * Get all roadmaps for a specific user
   */
  static async getUserRoadmaps(userId) {
    if (!userId) {
      throw new Error("User ID is required");
    }

    try {
      const metadataRef = collection(db, "roadmapMetadata");
      const q = query(
        metadataRef,
        where("userId", "==", userId),
        orderBy("lastAccessed", "desc")
      );

      const querySnapshot = await getDocs(q);
      const roadmaps = [];

      querySnapshot.forEach((doc) => {
        roadmaps.push({
          id: doc.id,
          ...doc.data(),
        });
      });

      return roadmaps;
    } catch (error) {
      console.error("❌ Error getting user roadmaps:", error);
      throw new Error("Failed to load user roadmaps: " + error.message);
    }
  }

  /**
   * Get public roadmaps for community section
   */
  static async getPublicRoadmaps(limitCount = 20) {
    try {
      const metadataRef = collection(db, "roadmapMetadata");

      // Try optimized query first, fall back to simple query if index not ready
      let q;
      try {
        q = query(
          metadataRef,
          where("isPublic", "==", true),
          orderBy("updatedAt", "desc"),
          limit(limitCount)
        );
      } catch (indexError) {
        console.log("📊 Using fallback query (index not ready)");
        q = query(
          metadataRef,
          where("isPublic", "==", true),
          limit(limitCount)
        );
      }

      const querySnapshot = await getDocs(q);
      const roadmaps = [];

      querySnapshot.forEach((doc) => {
        roadmaps.push({
          id: doc.id,
          ...doc.data(),
        });
      });

      // Sort in memory if we used the fallback query
      if (!q._query.orderBy || q._query.orderBy.length === 0) {
        roadmaps.sort((a, b) => {
          const aTime = a.updatedAt?.toDate?.() || new Date(a.updatedAt || 0);
          const bTime = b.updatedAt?.toDate?.() || new Date(b.updatedAt || 0);
          return bTime - aTime;
        });
      }

      return roadmaps;
    } catch (error) {
      console.error("❌ Error getting public roadmaps:", error);
      throw new Error("Failed to load public roadmaps: " + error.message);
    }
  }

  /**
   * Update roadmap privacy setting
   */
  static async updateRoadmapPrivacy(roadmapId, isPublic, userId) {
    if (!userId) {
      throw new Error("User must be authenticated");
    }

    try {
      const batch = writeBatch(db);

      // Update roadmap document
      const roadmapRef = doc(db, "roadmaps", roadmapId);
      batch.update(roadmapRef, {
        isPublic: isPublic,
        updatedAt: serverTimestamp(),
      });

      // Update metadata document
      const metadataRef = doc(db, "roadmapMetadata", roadmapId);
      batch.update(metadataRef, {
        isPublic: isPublic,
        updatedAt: serverTimestamp(),
      });

      await batch.commit();

      console.log(
        `✅ Roadmap privacy updated: ${roadmapId} -> ${
          isPublic ? "public" : "private"
        }`
      );
      return true;
    } catch (error) {
      console.error("❌ Error updating roadmap privacy:", error);
      throw new Error("Failed to update roadmap privacy: " + error.message);
    }
  }

  /**
   * Update roadmap data
   */
  static async updateRoadmap(roadmapId, roadmapData, userId) {
    if (!userId) {
      throw new Error("User must be authenticated");
    }

    try {
      // Compress the updated roadmap data
      console.log("🗜️ Compressing updated roadmap data...");
      const compressedData = await compressData(roadmapData);

      console.log(`📊 Update compression stats:
        - Original size: ${compressedData.originalSize} bytes
        - Compressed size: ${compressedData.compressedSize} bytes
        - Compression ratio: ${(
          (1 - compressedData.compressedSize / compressedData.originalSize) *
          100
        ).toFixed(1)}%`);

      const batch = writeBatch(db);

      // Update roadmap document with compressed data
      const roadmapRef = doc(db, "roadmaps", roadmapId);
      batch.update(roadmapRef, {
        data: compressedData,
        updatedAt: serverTimestamp(),
        lastAccessed: serverTimestamp(),
        version: increment(1),
        compressionInfo: {
          isCompressed: compressedData.isCompressed,
          originalSize: compressedData.originalSize,
          compressedSize: compressedData.compressedSize,
          method: compressedData.isCompressed ? "gzip" : "none",
        },
      });

      // Update metadata document
      const metadataRef = doc(db, "roadmapMetadata", roadmapId);
      batch.update(metadataRef, {
        title: roadmapData.title,
        description: roadmapData.description || "",
        projectLevel: roadmapData.project_level || "beginner",
        tags: roadmapData.tags || [],
        updatedAt: serverTimestamp(),
        lastAccessed: serverTimestamp(),
        totalPhases: this.calculateTotalPhases(roadmapData),
        totalTasks: this.calculateTotalTasks(roadmapData),
      });

      await batch.commit();

      console.log("✅ Roadmap updated in Firestore:", roadmapId);
      return true;
    } catch (error) {
      console.error("❌ Error updating roadmap:", error);
      throw new Error("Failed to update roadmap: " + error.message);
    }
  }

  /**
   * Delete a roadmap
   */
  static async deleteRoadmap(roadmapId, userId) {
    if (!userId) {
      throw new Error("User must be authenticated");
    }

    try {
      const batch = writeBatch(db);

      // Delete roadmap document
      const roadmapRef = doc(db, "roadmaps", roadmapId);
      batch.delete(roadmapRef);

      // Delete metadata document
      const metadataRef = doc(db, "roadmapMetadata", roadmapId);
      batch.delete(metadataRef);

      // Delete task completions
      const completionRef = doc(
        db,
        "taskCompletions",
        userId,
        "roadmaps",
        roadmapId
      );
      batch.delete(completionRef);

      await batch.commit();

      console.log("✅ Roadmap deleted from Firestore:", roadmapId);
      return true;
    } catch (error) {
      console.error("❌ Error deleting roadmap:", error);
      throw new Error("Failed to delete roadmap: " + error.message);
    }
  }

  /**
   * Update last accessed time
   */
  static async updateLastAccessed(roadmapId) {
    try {
      const metadataRef = doc(db, "roadmapMetadata", roadmapId);
      await updateDoc(metadataRef, {
        lastAccessed: serverTimestamp(),
      });
    } catch (error) {
      console.error("❌ Error updating last accessed:", error);
      // Don't throw error for this non-critical operation
    }
  }

  /**
   * Calculate total phases in roadmap
   */
  static calculateTotalPhases(roadmapData) {
    return roadmapData.phases ? roadmapData.phases.length : 0;
  }

  /**
   * Calculate total tasks in roadmap
   */
  static calculateTotalTasks(roadmapData) {
    if (!roadmapData.phases) return 0;

    return roadmapData.phases.reduce((total, phase) => {
      return total + (phase.tasks ? phase.tasks.length : 0);
    }, 0);
  }

  /**
   * Subscribe to user's roadmaps (real-time updates)
   */
  static subscribeToUserRoadmaps(userId, callback) {
    if (!userId) {
      throw new Error("User ID is required");
    }

    const metadataRef = collection(db, "roadmapMetadata");
    const q = query(
      metadataRef,
      where("userId", "==", userId),
      orderBy("lastAccessed", "desc")
    );

    return onSnapshot(
      q,
      (querySnapshot) => {
        const roadmaps = [];
        querySnapshot.forEach((doc) => {
          roadmaps.push({
            id: doc.id,
            ...doc.data(),
          });
        });
        callback(roadmaps);
      },
      (error) => {
        console.error("❌ Error in roadmaps subscription:", error);
        callback([]);
      }
    );
  }

  /**
   * Subscribe to public roadmaps (real-time updates)
   */
  static subscribeToPublicRoadmaps(callback, limitCount = 20) {
    const metadataRef = collection(db, "roadmapMetadata");

    // Try optimized query first, fall back to simple query if index not ready
    let q;
    try {
      q = query(
        metadataRef,
        where("isPublic", "==", true),
        orderBy("updatedAt", "desc"),
        limit(limitCount)
      );
    } catch (indexError) {
      console.log("📊 Using fallback subscription query (index not ready)");
      q = query(metadataRef, where("isPublic", "==", true), limit(limitCount));
    }

    return onSnapshot(
      q,
      (querySnapshot) => {
        const roadmaps = [];
        querySnapshot.forEach((doc) => {
          roadmaps.push({
            id: doc.id,
            ...doc.data(),
          });
        });

        // Sort in memory if we used the fallback query
        if (!q._query.orderBy || q._query.orderBy.length === 0) {
          roadmaps.sort((a, b) => {
            const aTime = a.updatedAt?.toDate?.() || new Date(a.updatedAt || 0);
            const bTime = b.updatedAt?.toDate?.() || new Date(b.updatedAt || 0);
            return bTime - aTime;
          });
        }

        callback(roadmaps);
      },
      (error) => {
        console.error("❌ Error in public roadmaps subscription:", error);
        callback([]);
      }
    );
  }
}

export default FirestorePersistence;
