/**
 * Firebase Configuration
 * Initialize Firebase app with authentication and Firestore
 */

import { initializeApp } from "firebase/app";
import { getAuth, connectAuthEmulator } from "firebase/auth";
import { getFirestore, connectFirestoreEmulator } from "firebase/firestore";

// Your Firebase configuration
// TODO: Replace these placeholder values with your actual Firebase project configuration
// You can find these values in your Firebase Console > Project Settings > General > Your apps
const firebaseConfig = {
  apiKey: "your-api-key-here",
  authDomain: "roadmap-cc3d6.firebaseapp.com",
  projectId: "roadmap-cc3d6",
  storageBucket: "roadmap-cc3d6.firebasestorage.app",
  messagingSenderId: "your-messaging-sender-id",
  appId: "your-app-id",
  measurementId: "your-measurement-id", // Optional, for Google Analytics
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase Authentication and get a reference to the service
export const auth = getAuth(app);

// Initialize Cloud Firestore and get a reference to the service
export const db = getFirestore(app);

// Connect to emulators in development (optional)
if (import.meta.env.DEV && !auth._delegate._config.emulator) {
  try {
    // Uncomment these lines if you want to use Firebase emulators in development
    // connectAuthEmulator(auth, "http://localhost:9099");
    // connectFirestoreEmulator(db, 'localhost', 8080);
  } catch (error) {
    console.log("Emulator connection failed:", error);
  }
}

export default app;
