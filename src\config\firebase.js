/**
 * Firebase Configuration
 * Initialize Firebase app with authentication and Firestore
 */

import { initializeApp } from "firebase/app";
import { getAuth, connectAuthEmulator } from "firebase/auth";
import { getFirestore, connectFirestoreEmulator } from "firebase/firestore";

// Firebase configuration
// Set VITE_FIREBASE_DEMO_MODE=true in your .env file to use demo mode
const isDemoMode = import.meta.env.VITE_FIREBASE_DEMO_MODE === "true";

const firebaseConfig = isDemoMode
  ? {
      // Demo configuration - uses Firebase emulators
      apiKey: "demo-api-key",
      authDomain: "demo-project.firebaseapp.com",
      projectId: "demo-project",
      storageBucket: "demo-project.appspot.com",
      messagingSenderId: "123456789",
      appId: "1:123456789:web:demo",
    }
  : {
      // Production configuration from environment variables
      apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
      authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
      projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
      storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
      messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
      appId: import.meta.env.VITE_FIREBASE_APP_ID,
      measurementId: import.meta.env.VITE_FIREBASE_MEASUREMENT_ID, // Optional, for Google Analytics
    };

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase Authentication and get a reference to the service
export const auth = getAuth(app);

// Initialize Cloud Firestore and get a reference to the service
export const db = getFirestore(app);

// Connect to emulators in development or demo mode
if (import.meta.env.DEV || isDemoMode) {
  try {
    // Connect to Firebase emulators for development/demo
    if (isDemoMode) {
      console.log("🔥 Firebase Demo Mode: Using emulated services");
      connectAuthEmulator(auth, "http://localhost:9099", {
        disableWarnings: true,
      });
      connectFirestoreEmulator(db, "localhost", 8080);
    }
    // Uncomment these lines if you want to use Firebase emulators in development
    // connectAuthEmulator(auth, "http://localhost:9099");
    // connectFirestoreEmulator(db, 'localhost', 8080);
  } catch (error) {
    console.log("Emulator connection failed:", error);
  }
}

export default app;
